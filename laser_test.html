<!DOCTYPE html>
<html>
<head>
    <title>Laser Plugin Test</title>
    <style>
        body { margin: 0; padding: 20px; background: #222; color: white; font-family: monospace; }
        #console { background: #000; padding: 10px; height: 200px; overflow-y: scroll; margin-bottom: 20px; }
        #canvas { border: 1px solid #555; background: #333; }
    </style>
</head>
<body>
    <h1>EventLaserLines Plugin Test</h1>
    <div id="console"></div>
    <canvas id="canvas" width="800" height="600"></canvas>
    
    <script>
        // Mock RPG Maker MZ classes for testing
        class Bitmap {
            constructor(width, height) {
                this.width = width;
                this.height = height;
                this.canvas = document.createElement('canvas');
                this.canvas.width = width;
                this.canvas.height = height;
                this.context = this.canvas.getContext('2d');
                console.log('Bitmap created:', width, 'x', height);
            }
            
            destroy() {
                console.log('Bitmap destroyed');
            }
            
            fillRect(x, y, width, height, color) {
                this.context.fillStyle = color;
                this.context.fillRect(x, y, width, height);
            }
        }
        
        class Sprite {
            constructor() {
                this.x = 0;
                this.y = 0;
                this.opacity = 255;
                this.visible = true;
                this.bitmap = null;
                this.children = [];
                this.parent = null;
                this.anchor = { x: 0, y: 0 };
                this.scale = { x: 1, y: 1 };
                this.skew = { x: 0, y: 0 };
                this.rotation = 0;
                this.tint = 0xffffff;
            }
            
            addChild(child) {
                this.children.push(child);
                child.parent = this;
                console.log('Child added to sprite, total children:', this.children.length);
            }
            
            removeChild(child) {
                const index = this.children.indexOf(child);
                if (index >= 0) {
                    this.children.splice(index, 1);
                    child.parent = null;
                }
            }
            
            update() {
                // Override in subclasses
            }
            
            destroy() {
                this.children.forEach(child => {
                    if (child.destroy) child.destroy();
                });
                this.children = [];
            }
        }
        
        // Mock console for display
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const consoleDiv = document.getElementById('console');
            consoleDiv.innerHTML += args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        // Test the laser creation
        console.log('Starting laser test...');
        console.log('Bitmap class available:', typeof Bitmap !== 'undefined');
        
        // Create a simple test laser
        try {
            const testBitmap = new Bitmap(200, 10);
            const ctx = testBitmap.context;
            
            // Draw a simple red laser
            ctx.fillStyle = '#FF0000';
            ctx.fillRect(0, 0, 200, 10);
            
            // Draw it on the main canvas
            const mainCanvas = document.getElementById('canvas');
            const mainCtx = mainCanvas.getContext('2d');
            mainCtx.drawImage(testBitmap.canvas, 100, 100);
            
            console.log('Test laser drawn successfully!');
            
        } catch (error) {
            console.error('Error creating test laser:', error);
        }
        
        console.log('Test complete. If you see a red rectangle above, bitmap creation is working.');
    </script>
</body>
</html>
