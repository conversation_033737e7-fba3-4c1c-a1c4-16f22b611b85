//=============================================================================
// EventLaserLines.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc Creates animated laser beams that emit from single events
 * <AUTHOR> Name
 * @version 1.0.0
 * 
 * @param laserColor
 * @text Laser Color
 * @desc The color of the laser lines (hex format).
 * @default #FF0066
 * @type string
 * 
 * @param laserWidth
 * @text Laser Width
 * @desc The width of the laser lines in pixels.
 * @default 3
 * @type number
 * @min 1
 * @max 10
 * 
 * @param laserOpacity
 * @text Laser Opacity
 * @desc The opacity of the laser lines (0-255).
 * @default 180
 * @type number
 * @min 50
 * @max 255
 * 
 * @param laserPulse
 * @text Laser Pulse
 * @desc Whether the laser should pulse/flicker.
 * @default true
 * @type boolean
 * 
 * @param laserPulseSpeed
 * @text Pulse Speed
 * @desc How fast the laser pulses (higher = faster).
 * @default 0.1
 * @type number
 * @min 0.01
 * @max 0.5
 * @decimals 2
 * 
 * @param laserGlow
 * @text Laser Glow
 * @desc Whether to add a glow effect around the laser.
 * @default true
 * @type boolean
 * 
 * @param laserGlowSize
 * @text Glow Size
 * @desc The size of the glow effect around the laser.
 * @default 8
 * @type number
 * @min 0
 * @max 20
 * 
 * @param laserFloat
 * @text Laser Float
 * @desc Whether the laser should have a floating animation.
 * @default true
 * @type boolean
 * 
 * @param laserParticles
 * @text Laser Particles
 * @desc Whether to add animated particles along the laser.
 * @default true
 * @type boolean
 * 
 * @param laserFlicker
 * @text Laser Flicker
 * @desc Whether the laser should have a dangerous flicker effect.
 * @default true
 * @type boolean
 * 
 * @param laserFlickerIntensity
 * @text Flicker Intensity
 * @desc How intense the flicker effect is (0.1-0.8).
 * @default 0.3
 * @type number
 * @min 0.1
 * @max 0.8
 * @decimals 1
 * 
 * @param laserHeatDistortion
 * @text Heat Distortion
 * @desc Whether to add heat distortion effects to the laser.
 * @default true
 * @type boolean
 * 
 * @param laserElectricArcs
 * @text Electric Arcs
 * @desc Whether to add dangerous electric arc effects along the laser.
 * @default true
 * @type boolean
 * 
 * @param laserWarningPulse
 * @text Warning Pulse
 * @desc Whether the laser should flash warning red colors.
 * @default true
 * @type boolean
 * 
 * @help
 * ============================================================================
 * Event Laser Lines Plugin
 * ============================================================================
 * 
 * This plugin creates animated laser beams that emit from single events
 * 
 * ============================================================================
 * Setup Instructions
 * ============================================================================
 * 
 * 1. Place an event where you want the laser to emit from
 * 2. Add a notetag to the event:
 * 
 * Basic laser:
 * <laser>
 * 
 * Custom laser:
 * <laser:direction=right,length=100,color=#00FFFF>
 * 
 * Dangerous laser with all effects:
 * <laser:direction=up,length=120,color=#FF0000,flicker=true,electricArcs=true,warningPulse=true>
 * 
 * Multiple lasers on same event:
 * <laser 1>
 * <laser 2:direction=up,length=80>
 * 
 * ============================================================================
 * Notetag Examples
 * ============================================================================
 * 
 * Basic laser:
 * <laser>
 * 
 * Directional laser:
 * <laser:direction=right,length=120>
 * <laser:direction=up,length=80>
 * <laser:direction=left,length=100>
 * <laser:direction=down,length=60>
 * 
 * Custom laser properties:
 * <laser:direction=right,length=100,color=#00FFFF,width=5,pulse=false>
 * 
 * Multiple lasers:
 * <laser 1:direction=right,length=100>
 * <laser 2:direction=up,length=80>
 * <laser 3:direction=left,length=120>
 * 
 * ============================================================================
 * Plugin Parameters
 * ============================================================================
 * 
 * - laserColor: Base color of all lasers (can be overridden per laser)
 * - laserWidth: Default width of laser lines
 * - laserOpacity: Default opacity of laser lines
 * - laserPulse: Whether lasers pulse by default
 * - laserPulseSpeed: Speed of the pulse effect
 * - laserGlow: Whether to add glow effects
 * - laserGlowSize: Size of glow effects
 * 
 * ============================================================================
 * Version History
 * ============================================================================
 * 
 * v1.0.0 - Initial release
 * 
 * ============================================================================
 */

(() => {
    'use strict';

    console.log('EventLaserLines: Plugin loading...');



    //=============================================================================
    // Plugin Parameters
    //=============================================================================
    const parameters = PluginManager.parameters('EventLaserLines');
    const LASER_COLOR = parameters['laserColor'] || '#FF0066';
    const LASER_WIDTH = Number(parameters['laserWidth']) || 3;
    const LASER_OPACITY = Number(parameters['laserOpacity']) || 180;
    const LASER_PULSE = parameters['laserPulse'] === 'true';
    const LASER_PULSE_SPEED = Number(parameters['laserPulseSpeed']) || 0.1;
    const LASER_GLOW = parameters['laserGlow'] === 'true';
    const LASER_GLOW_SIZE = Number(parameters['laserGlowSize']) || 8;
    const LASER_FLOAT = parameters['laserFloat'] === 'true';
    const LASER_PARTICLES = parameters['laserParticles'] === 'true';
    const LASER_FLICKER = parameters['laserFlicker'] === 'true';
    const LASER_FLICKER_INTENSITY = Number(parameters['laserFlickerIntensity']) || 0.3;
    const LASER_HEAT_DISTORTION = parameters['laserHeatDistortion'] === 'true';
    const LASER_ELECTRIC_ARCS = parameters['laserElectricArcs'] === 'true';
    const LASER_WARNING_PULSE = parameters['laserWarningPulse'] === 'true';

    //=============================================================================
    // Constants for Magic Numbers
    //=============================================================================

    const DEFAULT_TILE_HEIGHT = 48; // RPG Maker default tile height
    const DEFAULT_LENGTH_MIN = 10;
    const DEFAULT_LENGTH_MAX = 1000;
    const DEFAULT_WIDTH_MIN = 1;
    const DEFAULT_WIDTH_MAX = 20;
    const DEFAULT_OPACITY_MIN = 0;
    const DEFAULT_OPACITY_MAX = 255;
    const DEFAULT_GLOW_SIZE_MIN = 0;
    const DEFAULT_GLOW_SIZE_MAX = 50;

    // Animation constants
    const PULSE_AMPLITUDE = 0.3;
    const PULSE_OFFSET = 0.7;
    const GLOW_AMPLITUDE = 0.2;
    const GLOW_OFFSET = 0.8;
    const FLOAT_AMPLITUDE = 2;
    const HEAT_DISTORTION_AMPLITUDE = 0.5;
    const HEAT_DISTORTION_SCALE = 0.01;
    const ARC_ROTATION_AMPLITUDE = 0.01;

    // Electric arc constants
    const ARC_COUNT = 2;
    const ARC_LENGTH = 15;
    const ARC_SEGMENTS = 3;
    const ARC_JAGGEDNESS = 8;
    const ARC_LINE_WIDTH = 2;
    const ARC_GLOW_WIDTH = 4;

    // Animation speed constants
    const GLOW_SPEED = 0.05;
    const FLOAT_SPEED = 0.03;
    const HEAT_SPEED = 0.08;
    const ARC_SPEED = 0.02;
    const WARNING_SPEED = 0.08;
    const FLICKER_SPEED = 0.15;

    //=============================================================================
    // Helper Functions
    //=============================================================================

    // Standardized boolean parameter handling
    function parseBooleanSetting(value, defaultValue) {
        if (value === undefined || value === null) {
            return defaultValue;
        }
        if (typeof value === 'boolean') {
            return value;
        }
        if (typeof value === 'string') {
            return value.toLowerCase() === 'true';
        }
        return defaultValue;
    }

    // Performance optimization: Pre-calculated sine lookup table
    const SINE_LOOKUP_SIZE = 360;
    const SINE_LOOKUP = [];
    for (let i = 0; i < SINE_LOOKUP_SIZE; i++) {
        SINE_LOOKUP[i] = Math.sin((i * Math.PI * 2) / SINE_LOOKUP_SIZE);
    }

    // Fast sine approximation using lookup table
    function fastSin(angle) {
        const index = Math.floor(((angle % (Math.PI * 2)) / (Math.PI * 2)) * SINE_LOOKUP_SIZE);
        return SINE_LOOKUP[Math.abs(index) % SINE_LOOKUP_SIZE];
    }

    // Input validation functions
    function validateHexColor(color) {
        if (!color || typeof color !== 'string') {
            return LASER_COLOR;
        }

        // Remove # if present
        const cleanColor = color.replace('#', '');

        // Check if it's a valid hex color (3 or 6 characters)
        if (/^[0-9A-Fa-f]{3}$/.test(cleanColor)) {
            // Convert 3-digit hex to 6-digit
            return '#' + cleanColor.split('').map(c => c + c).join('');
        } else if (/^[0-9A-Fa-f]{6}$/.test(cleanColor)) {
            return '#' + cleanColor;
        }

        // Invalid color, return default
        console.warn(`EventLaserLines: Invalid hex color "${color}", using default`);
        return LASER_COLOR;
    }

    function validateNumber(value, defaultValue, min = -Infinity, max = Infinity) {
        const num = Number(value);
        if (isNaN(num)) {
            return defaultValue;
        }
        return Math.max(min, Math.min(max, num));
    }

    function validateDirection(direction) {
        const validDirections = ['right', 'up', 'left', 'down'];
        if (validDirections.includes(direction)) {
            return direction;
        }
        console.warn(`EventLaserLines: Invalid direction "${direction}", using "right"`);
        return 'right';
    }

    //=============================================================================
    // Laser Line Sprite
    //=============================================================================
    class LaserLine extends Sprite {
        constructor(x, y, direction, length, settings) {
            super();
            this.x = x;
            this.y = y;
            this.originalY = y; // Store original Y for floating animation
            this.direction = direction;
            this.length = length;
            this.settings = settings;
            this.pulseTime = 0;
            this.glowTime = 0;
            this.floatTime = 0;
            this.heatTime = 0;
            this.flickerTime = 0;
            this.arcTime = 0;
            this.warningTime = 0;

            // Performance optimization: Cache frequently used values
            this.baseOpacity = this.settings.opacity || LASER_OPACITY;
            this.pulseEnabled = parseBooleanSetting(this.settings.pulse, LASER_PULSE);
            this.flickerEnabled = parseBooleanSetting(this.settings.flicker, LASER_FLICKER);
            this.glowEnabled = parseBooleanSetting(this.settings.glow, LASER_GLOW);
            this.particlesEnabled = parseBooleanSetting(this.settings.particles, LASER_PARTICLES);
            this.floatEnabled = parseBooleanSetting(this.settings.float, LASER_FLOAT);
            this.heatDistortionEnabled = parseBooleanSetting(this.settings.heatDistortion, LASER_HEAT_DISTORTION);
            this.electricArcsEnabled = parseBooleanSetting(this.settings.electricArcs, LASER_ELECTRIC_ARCS);
            this.warningPulseEnabled = parseBooleanSetting(this.settings.warningPulse, LASER_WARNING_PULSE);

            this.createLaserBitmap();

            // Ensure laser is visible
            this.visible = true;
            this.opacity = this.baseOpacity;

            console.log('EventLaserLines: LaserLine created at', this.x, this.y, 'direction:', this.direction, 'length:', this.length);
            console.log('EventLaserLines: Bitmap created:', !!this.bitmap, 'visible:', this.visible, 'opacity:', this.opacity);
            console.log('EventLaserLines: Bitmap dimensions:', this.bitmap ? `${this.bitmap.width}x${this.bitmap.height}` : 'none');
        }

        createLaserBitmap() {
            try {
                // Get direction angle
                const directionAngles = {
                    'right': 0,
                    'up': -Math.PI / 2,
                    'left': Math.PI,
                    'down': Math.PI / 2
                };

                const angle = directionAngles[this.direction] || 0;
                const length = Math.max(this.length, DEFAULT_LENGTH_MIN);
                const height = Math.max(this.settings.width || LASER_WIDTH, 3);

                // Create bitmap with extra space for glow
                const extraSpace = parseBooleanSetting(this.settings.glow, LASER_GLOW) ? (this.settings.glowSize || LASER_GLOW_SIZE) * 2 : 0;
                const width = length + extraSpace;
                const bitmapHeight = height + extraSpace;

                const bitmap = new Bitmap(width, bitmapHeight);
                const ctx = bitmap.context;

            // Save initial context state
            ctx.save();

            // Center the drawing context
            ctx.translate(extraSpace / 2, bitmapHeight / 2);

            // Apply rotation
            ctx.rotate(angle);

            // Create laser line
            this.drawLaserLine(ctx, length);

            // Add glow effect if enabled
            if (parseBooleanSetting(this.settings.glow, LASER_GLOW)) {
                this.drawLaserGlow(ctx, length);
            }

            // Restore context state
            ctx.restore();

            this.bitmap = bitmap;
            this.anchor.x = 0.5;
            this.anchor.y = 0.5;

            // Ensure sprite has proper dimensions
            this.width = width;
            this.height = bitmapHeight;
            } catch (error) {
                console.error('EventLaserLines: Error creating laser bitmap:', error);
                // Create a simple fallback bitmap
                this.bitmap = new Bitmap(100, 10);
                this.bitmap.fillRect(0, 0, 100, 10, '#FF0000');
            }
        }

        drawLaserLine(ctx, length) {
            const width = validateNumber(this.settings.width, LASER_WIDTH, DEFAULT_WIDTH_MIN, DEFAULT_WIDTH_MAX);
            const color = validateHexColor(this.settings.color);
            const opacity = validateNumber(this.settings.opacity, LASER_OPACITY, DEFAULT_OPACITY_MIN, DEFAULT_OPACITY_MAX);

            // Convert hex to RGB (color is now guaranteed to be valid)
            const hexColor = color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Save context state before drawing
            ctx.save();

            // Create realistic laser beam with multiple layers

            // 1. Outer atmospheric glow (very faint, wide)
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.05})`;
            ctx.lineWidth = width * 4;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();

            // 2. Medium atmospheric scattering
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.15})`;
            ctx.lineWidth = width * 2.5;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();

            // 3. Inner beam glow
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.4})`;
            ctx.lineWidth = width * 1.5;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();

            // 4. Core laser beam (brightest, sharpest)
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.9})`;
            ctx.lineWidth = width;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();

            // 5. Intense white-hot core center
            ctx.strokeStyle = `rgba(255, 255, 255, ${opacity / 255 * 0.6})`;
            ctx.lineWidth = Math.max(1, width * 0.4);
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();

            // 6. Add electric arc effects along the beam for danger
            if (parseBooleanSetting(this.settings.electricArcs, LASER_ELECTRIC_ARCS)) {
                this.drawElectricArcs(ctx, length, r, g, b, opacity);
            }

            // Restore context state
            ctx.restore();
        }

        drawLaserGlow(ctx, length) {
            const width = validateNumber(this.settings.width, LASER_WIDTH, DEFAULT_WIDTH_MIN, DEFAULT_WIDTH_MAX);
            const glowSize = validateNumber(this.settings.glowSize, LASER_GLOW_SIZE, DEFAULT_GLOW_SIZE_MIN, DEFAULT_GLOW_SIZE_MAX);
            const color = validateHexColor(this.settings.color);

            // Convert hex to RGB (color is now guaranteed to be valid)
            const hexColor = color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Save context state before drawing glow
            ctx.save();

            // Create realistic atmospheric scattering effect
            // This simulates how laser light interacts with air particles

            // 1. Wide, very faint atmospheric glow
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.02)`;
            ctx.lineWidth = width + glowSize * 3;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();

            // 2. Medium atmospheric scattering
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.04)`;
            ctx.lineWidth = width + glowSize * 2;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();

            // 3. Closer atmospheric effect
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.08)`;
            ctx.lineWidth = width + glowSize;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();

            // Restore context state
            ctx.restore();

            // Particle functionality removed - no more circles along the laser
        }
        

        
        drawElectricArcs(ctx, length, r, g, b, opacity) {
            // Save context state before drawing arcs
            ctx.save();

            // Optimized electric arcs - reduced complexity for maximum performance
            const arcCount = ARC_COUNT; // Reduced from 4 to 2 arcs for performance
            const arcIntensity = opacity / 255 * 0.9; // Brighter for visibility

            for (let i = 0; i < arcCount; i++) {
                const x = (i + 1) * (length / (arcCount + 1));
                const arcLength = ARC_LENGTH; // Fixed size instead of random for performance
                const segments = ARC_SEGMENTS; // Fixed segments instead of random for performance

                // Draw jagged white core arc
                ctx.strokeStyle = `rgba(255, 255, 255, ${arcIntensity})`;
                ctx.lineWidth = ARC_LINE_WIDTH;
                ctx.lineCap = 'round';

                // Create jagged path like the sparking plugin - centered on laser line
                const startX = x - arcLength / 2;
                const startY = 0; // Center on laser line (y=0)
                const endX = x + arcLength / 2;
                const endY = 0; // Center on laser line (y=0)

                ctx.beginPath();
                ctx.moveTo(startX, startY);

                // Create jagged segments for realistic electric arc
                for (let j = 1; j < segments; j++) {
                    const t = j / segments;
                    const baseX = startX + (endX - startX) * t;
                    const baseY = startY + (endY - startY) * t;

                    // Add random jaggedness perpendicular to the arc (like the sparking plugin)
                    const jag = (Math.random() - 0.5) * ARC_JAGGEDNESS; // Random offset for jaggedness
                    const jagX = baseX;
                    const jagY = baseY + jag;

                    ctx.lineTo(jagX, jagY);
                }

                ctx.lineTo(endX, endY);
                ctx.stroke();

                // Add colored glow around the white core (like the sparking plugin)
                ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${arcIntensity * 0.7})`;
                ctx.lineWidth = ARC_GLOW_WIDTH;
                ctx.stroke();

                // Secondary arcs removed for maximum performance optimization
            }

            // Restore context state
            ctx.restore();
        }

        update() {
            super.update();

            // Check if we need to create the bitmap
            if (this._needsBitmapCreation && typeof Bitmap !== 'undefined') {
                console.log('EventLaserLines: Creating deferred bitmap');
                this._needsBitmapCreation = false;
                this.createLaserBitmap();
            }

            // Skip update if no bitmap is available yet
            if (!this.bitmap) {
                return;
            }

            // Use cached base opacity
            let opacityMultiplier = 1.0;

            // Update pulse effect (using cached boolean and fast sine)
            if (this.pulseEnabled) {
                this.pulseTime += LASER_PULSE_SPEED;
                const pulseFactor = PULSE_OFFSET + fastSin(this.pulseTime) * PULSE_AMPLITUDE;
                opacityMultiplier *= pulseFactor;
            }

            // Add dangerous flicker effect (combines with pulse)
            if (this.flickerEnabled) {
                this.flickerTime += FLICKER_SPEED;
                const flickerFactor = 1 + fastSin(this.flickerTime * 3) * LASER_FLICKER_INTENSITY;
                opacityMultiplier *= flickerFactor;
            }

            // Apply combined opacity effects
            this.opacity = this.baseOpacity * opacityMultiplier;

            // Update glow animation (using cached booleans)
            if (this.glowEnabled && this.particlesEnabled) {
                this.glowTime += GLOW_SPEED;
                const glowFactor = GLOW_OFFSET + fastSin(this.glowTime) * GLOW_AMPLITUDE;
                this.scale.x = glowFactor;
                this.scale.y = glowFactor;
            }

            // Update floating animation
            if (this.floatEnabled) {
                this.floatTime += FLOAT_SPEED;
                const floatOffset = fastSin(this.floatTime) * FLOAT_AMPLITUDE;
                this.y = this.originalY + floatOffset;
            }

            // Add subtle heat distortion effect
            if (this.heatDistortionEnabled) {
                this.heatTime += HEAT_SPEED;
                const heatDistortion = fastSin(this.heatTime) * HEAT_DISTORTION_AMPLITUDE;
                this.skew.x = heatDistortion * HEAT_DISTORTION_SCALE; // Very subtle skew effect
            }

            // Electric arc animation - subtle effects without making laser invisible
            if (this.electricArcsEnabled) {
                this.arcTime += ARC_SPEED;
                // Minimal movement to reduce calculations
                this.rotation = fastSin(this.arcTime) * ARC_ROTATION_AMPLITUDE;
            }

            // Add warning pulse effect (red flash)
            if (this.warningPulseEnabled) {
                this.warningTime += WARNING_SPEED;
                // Flash between normal color and warning red
                if (fastSin(this.warningTime) > 0) {
                    this.tint = 0xff0000; // Red warning tint
                } else {
                    this.tint = 0xffffff; // Normal color
                }
            }
        }

        destroy() {
            // Properly dispose of bitmap to prevent memory leaks
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }

            // Call parent destroy
            super.destroy();
        }
    }

    //=============================================================================
    // Laser Manager
    //=============================================================================
    class LaserManager {
        constructor(scene) {
            this.scene = scene;
            this.activeLasers = [];
            this.laserEvents = new Map();
            this.lastEventState = new Map(); // Track event states to detect changes
            this.needsUpdate = true; // Flag to control when to update
            this.notetagCache = new Map(); // Cache parsed notetags to avoid re-parsing
            this.lastMapId = -1; // Track map changes

            // Create laser layer
            this.laserLayer = new Sprite();
            this.laserLayer.z = 2; // Between map tiles and characters

            // Ensure the layer is visible and properly configured
            this.laserLayer.visible = true;
            this.laserLayer.opacity = 255;

            // Try to add to the map layer specifically to get below characters
            if (scene._spriteset && scene._spriteset._tilemap) {
                // Add as child of the tilemap (map layer) to ensure it's below characters
                scene._spriteset._tilemap.addChild(this.laserLayer);
                console.log('EventLaserLines: Laser layer added to tilemap (below characters)');
                console.log('EventLaserLines: Tilemap children count:', scene._spriteset._tilemap.children.length);
            } else if (scene._spriteset) {
                // Fallback to spriteset
                scene._spriteset.addChild(this.laserLayer);
                console.log('EventLaserLines: Laser layer added to spriteset (fallback)');
                console.log('EventLaserLines: Spriteset children count:', scene._spriteset.children.length);
            } else {
                console.warn('EventLaserLines: No suitable parent found for laser layer');
            }
        }

        initialize() {
            console.log('EventLaserLines: LaserManager initialize() called');

            // Force initial update
            this.needsUpdate = true;

            // Scan for laser events immediately
            this.scanEventsForLasers();
            console.log('EventLaserLines: Found laser events:', this.laserEvents.size);
            this.updateLasers();
            console.log('EventLaserLines: Active lasers after update:', this.activeLasers.length);

            console.log('EventLaserLines: LaserManager initialization complete');

            // Create a test laser to verify the system works
            this.createTestLaser();
        }

        // Check if events have changed and need laser updates
        hasEventsChanged() {
            // Check if we need to force an update
            if (this.needsUpdate) {
                return true;
            }

            // If this is the first run and we have no previous state, always update
            if (this.lastEventState.size === 0 && this.laserEvents.size > 0) {
                return true;
            }

            // Check if any event positions or pages have changed
            for (const [eventId] of this.laserEvents) {
                const event = $gameMap.event(eventId);
                if (!event) continue;

                const currentState = {
                    x: event.x,
                    y: event.y,
                    pageIndex: this.getCurrentPageIndex(event),
                    direction: event.direction()
                };

                const lastState = this.lastEventState.get(eventId);
                if (!lastState ||
                    lastState.x !== currentState.x ||
                    lastState.y !== currentState.y ||
                    lastState.pageIndex !== currentState.pageIndex ||
                    lastState.direction !== currentState.direction) {

                    this.lastEventState.set(eventId, currentState);
                    return true;
                }
            }

            return false;
        }

        scanEventsForLasers() {
            // Clear map if we're on a different map
            const currentMapId = $gameMap.mapId();
            if (this.lastMapId !== currentMapId) {
                this.laserEvents.clear();
                this.notetagCache.clear();
                this.lastMapId = currentMapId;
            }

            // Scan all events for laser notetags
            $gameMap.events().forEach((event, index) => {
                if (event && event.event()) {
                    const eventData = event.event();
                    const eventId = index + 1;

                    // Create cache key for this event's current state
                    const currentPageIndex = this.getCurrentPageIndex(event);
                    const cacheKey = `${eventId}_${currentPageIndex}`;

                    // Check if we have cached notetags for this event state
                    if (this.notetagCache.has(cacheKey)) {
                        const cachedNotetags = this.notetagCache.get(cacheKey);
                        if (cachedNotetags.length > 0) {
                            this.laserEvents.set(eventId, cachedNotetags);
                        }
                        return; // Skip parsing, use cached result
                    }

                    let allNotetags = [];

                    // Check event note field (always active regardless of page)
                    if (eventData.note) {
                        const notetagsFromNote = this.parseLaserNotetags(eventData.note);
                        allNotetags = allNotetags.concat(notetagsFromNote);
                    }

                    // Check Comment commands ONLY in the currently active page
                    if (eventData.pages && eventData.pages.length > 0) {
                        if (currentPageIndex >= 0 && currentPageIndex < eventData.pages.length) {
                            const currentPage = eventData.pages[currentPageIndex];
                            const commentText = this.extractCommentText(currentPage.list);
                            if (commentText) {
                                const notetagsFromComments = this.parseLaserNotetags(commentText);
                                allNotetags = allNotetags.concat(notetagsFromComments);
                            }
                        }
                    }

                    // Cache the result
                    this.notetagCache.set(cacheKey, allNotetags);

                    if (allNotetags.length > 0) {
                        this.laserEvents.set(eventId, allNotetags);
                    }
                }
            });
        }

        getCurrentPageIndex(event) {
            // Null safety check
            if (!event) {
                return -1;
            }

            // Use RPG Maker's built-in method to get the current page index
            // This respects all the page conditions (switches, variables, etc.)
            if (event.findProperPageIndex) {
                return event.findProperPageIndex();
            }

            // Fallback: manually check page conditions
            const eventData = event.event();
            if (!eventData || !eventData.pages || !Array.isArray(eventData.pages)) {
                return -1;
            }

            for (let i = eventData.pages.length - 1; i >= 0; i--) {
                const page = eventData.pages[i];
                if (page && this.meetsConditions(page.conditions, event)) {
                    return i;
                }
            }

            return -1;
        }

        meetsConditions(conditions, event) {
            // Check if page conditions are met
            if (!conditions) return true;

            // Check switch conditions
            if (conditions.switch1Valid && !$gameSwitches.value(conditions.switch1Id)) {
                return false;
            }
            if (conditions.switch2Valid && !$gameSwitches.value(conditions.switch2Id)) {
                return false;
            }

            // Check variable conditions
            if (conditions.variableValid) {
                const variableValue = $gameVariables.value(conditions.variableId);
                if (variableValue < conditions.variableValue) {
                    return false;
                }
            }

            // Check self switch conditions
            if (conditions.selfSwitchValid && event) {
                const key = [$gameMap.mapId(), event._eventId, conditions.selfSwitchCh];
                if (!$gameSelfSwitches.value(key)) {
                    return false;
                }
            }

            return true;
        }

        extractCommentText(commandList) {
            let commentText = '';

            if (!commandList || !Array.isArray(commandList)) {
                return commentText;
            }

            for (let i = 0; i < commandList.length; i++) {
                const command = commandList[i];

                // Null safety check for command
                if (!command || typeof command.code !== 'number') {
                    continue;
                }

                // Event command code 108 = Comment (first line)
                // Event command code 408 = Comment (continuation lines)
                if (command.code === 108 || command.code === 408) {
                    if (command.parameters && Array.isArray(command.parameters) && command.parameters[0]) {
                        commentText += command.parameters[0] + '\n';
                    }
                }
            }

            return commentText;
        }

        parseLaserNotetags(note) {
            const notetags = [];

            // Null safety check
            if (!note || typeof note !== 'string') {
                return notetags;
            }

            // Match both <laser> and <laser:...> formats
            const matches = note.match(/<laser(?:\s+(\d+))?(?::(.*?))?>/g);
            if (!matches) return notetags;

            matches.forEach((match) => {
                const fullMatch = match.match(/<laser(?:\s+(\d+))?(?::(.*?))?>/);
                if (!fullMatch) return;

                const laserId = fullMatch[1] ? Number(fullMatch[1]) : 1;
                const params = fullMatch[2] || '';

                const settings = {
                    direction: 'right',
                    length: 100,
                    color: LASER_COLOR,
                    width: LASER_WIDTH,
                    opacity: LASER_OPACITY,
                    pulse: LASER_PULSE,
                    glow: LASER_GLOW,
                    glowSize: LASER_GLOW_SIZE,
                    float: LASER_FLOAT,
                    particles: LASER_PARTICLES
                };

                // Parse custom parameters with validation
                if (params) {
                    params.split(',').forEach(param => {
                        const [key, value] = param.trim().split('=').map(p => p.trim());

                        if (key === 'direction') {
                            settings.direction = validateDirection(value);
                        } else if (key === 'length') {
                            settings.length = validateNumber(value, 100, DEFAULT_LENGTH_MIN, DEFAULT_LENGTH_MAX);
                        } else if (key === 'color') {
                            settings.color = validateHexColor(value);
                        } else if (key === 'width') {
                            settings.width = validateNumber(value, LASER_WIDTH, DEFAULT_WIDTH_MIN, DEFAULT_WIDTH_MAX);
                        } else if (key === 'opacity') {
                            settings.opacity = validateNumber(value, LASER_OPACITY, DEFAULT_OPACITY_MIN, DEFAULT_OPACITY_MAX);
                        } else if (key === 'pulse') {
                            settings.pulse = parseBooleanSetting(value, LASER_PULSE);
                        } else if (key === 'glow') {
                            settings.glow = parseBooleanSetting(value, LASER_GLOW);
                        } else if (key === 'glowSize') {
                            settings.glowSize = validateNumber(value, LASER_GLOW_SIZE, DEFAULT_GLOW_SIZE_MIN, DEFAULT_GLOW_SIZE_MAX);
                        } else if (key === 'float') {
                            settings.float = parseBooleanSetting(value, LASER_FLOAT);
                        } else if (key === 'particles') {
                            settings.particles = parseBooleanSetting(value, LASER_PARTICLES);
                        }
                    });
                }

                notetags.push({
                    laserId: laserId,
                    settings: settings
                });
            });

            return notetags;
        }

        updateLasers() {
            // Only update if events have changed
            if (!this.hasEventsChanged()) {
                return;
            }

            // Clear old lasers with proper cleanup
            this.activeLasers.forEach(laser => {
                if (laser.parent) {
                    laser.parent.removeChild(laser);
                }
                // Properly destroy laser and its bitmap
                if (laser.destroy) {
                    laser.destroy();
                }
            });
            this.activeLasers = [];

            // Re-scan events to get current page laser notetags
            this.scanEventsForLasers();

            // Create new lasers based on current laser events
            this.laserEvents.forEach((notetags, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) {
                    return;
                }

                // Get event sprite position with error handling
                if (!this.scene || !this.scene._spriteset || !this.scene._spriteset.findTargetSprite) {
                    console.warn('EventLaserLines: Scene or spriteset not available');
                    return;
                }

                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) {
                    return;
                }

                const eventX = sprite.x;
                // Adjust Y to center of tile instead of bottom
                // RPG Maker tiles are 48x48 pixels, and sprite Y is at bottom of tile
                const tileHeight = $gameMap.tileHeight ? $gameMap.tileHeight() : DEFAULT_TILE_HEIGHT;
                const eventY = sprite.y - (tileHeight / 2);

                // Create laser for each notetag
                if (!Array.isArray(notetags)) {
                    console.warn('EventLaserLines: Invalid notetags array for event', eventId);
                    return;
                }

                notetags.forEach((notetag) => {
                    try {
                        if (!notetag || !notetag.settings) {
                            console.warn('EventLaserLines: Invalid notetag structure');
                            return;
                        }

                        const laser = new LaserLine(
                            eventX,
                            eventY,
                            notetag.settings.direction,
                            notetag.settings.length,
                            notetag.settings
                        );

                        // Add to active lasers and layer
                        this.activeLasers.push(laser);
                        if (this.laserLayer && this.laserLayer.addChild) {
                            this.laserLayer.addChild(laser);
                            console.log('EventLaserLines: Laser added to layer. Layer children:', this.laserLayer.children.length);
                            console.log('EventLaserLines: Laser properties - x:', laser.x, 'y:', laser.y, 'visible:', laser.visible, 'opacity:', laser.opacity);
                        } else {
                            console.warn('EventLaserLines: Could not add laser to layer');
                        }
                    } catch (error) {
                        console.error(`EventLaserLines: Error creating laser ${notetag.laserId}:`, error);
                    }
                });
            });

            // Reset the update flag
            this.needsUpdate = false;
        }

        // Force an update on next frame (call this when events change)
        requestUpdate() {
            this.needsUpdate = true;
        }

        // Update method that should be called every frame (but only updates when needed)
        update() {
            this.updateLasers();
        }



        destroy() {
            // Clean up active lasers with proper disposal
            this.activeLasers.forEach(laser => {
                if (laser.parent) {
                    laser.parent.removeChild(laser);
                }
                // Ensure laser's own destroy method is called for bitmap cleanup
                if (laser.destroy) {
                    laser.destroy();
                }
            });
            this.activeLasers = [];
            this.laserEvents.clear();
            this.lastEventState.clear();
            this.notetagCache.clear();

            // Clean up laser layer
            if (this.laserLayer && this.laserLayer.parent) {
                this.laserLayer.parent.removeChild(this.laserLayer);
            }
            if (this.laserLayer) {
                this.laserLayer.destroy();
                this.laserLayer = null;
            }
        }

        // Debug method to create a test laser
        createTestLaser() {
            console.log('EventLaserLines: Creating test laser...');
            try {
                const testLaser = new LaserLine(
                    400, // x position (center of screen)
                    300, // y position (center of screen)
                    'right', // direction
                    200, // length
                    {
                        color: '#FF0066',
                        width: 8,
                        opacity: 255,
                        pulse: true,
                        glow: true,
                        glowSize: 10
                    }
                );

                this.activeLasers.push(testLaser);
                this.laserLayer.addChild(testLaser);

                console.log('EventLaserLines: Test laser created successfully');
                console.log('EventLaserLines: Test laser parent:', !!testLaser.parent);
                console.log('EventLaserLines: Laser layer children count:', this.laserLayer.children.length);
                console.log('EventLaserLines: Active lasers count:', this.activeLasers.length);
                console.log('EventLaserLines: Test laser position:', testLaser.x, testLaser.y);
                console.log('EventLaserLines: Test laser visible:', testLaser.visible, 'opacity:', testLaser.opacity);
            } catch (error) {
                console.error('EventLaserLines: Error creating test laser:', error);
            }
        }
    }

    //=============================================================================
    // Scene_Map Integration
    //=============================================================================
    const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
    Scene_Map.prototype.createSpriteset = function() {
        console.log('EventLaserLines: Scene_Map createSpriteset called');
        console.log('EventLaserLines: Bitmap class available:', typeof Bitmap !== 'undefined');
        _Scene_Map_createSpriteset.call(this);
        console.log('EventLaserLines: Spriteset created, tilemap available:', !!this._spriteset._tilemap);
        console.log('EventLaserLines: Creating LaserManager...');
        this._laserManager = new LaserManager(this);
        console.log('EventLaserLines: Initializing LaserManager...');
        this._laserManager.initialize();
        console.log('EventLaserLines: LaserManager setup complete');
    };

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);
        if (this._laserManager) {
            this._laserManager.update();
        }
    };

    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        if (this._laserManager) {
            this._laserManager.destroy();
        }
        _Scene_Map_terminate.call(this);
    };

    // Hook into map refresh to update lasers when needed
    const _Game_Map_refresh = Game_Map.prototype.refresh;
    Game_Map.prototype.refresh = function() {
        _Game_Map_refresh.call(this);
        if (SceneManager._scene && SceneManager._scene._laserManager) {
            SceneManager._scene._laserManager.requestUpdate();
        }
    };

    // Hook into event page changes to update lasers
    const _Game_Event_refresh = Game_Event.prototype.refresh;
    Game_Event.prototype.refresh = function() {
        _Game_Event_refresh.call(this);
        if (SceneManager._scene && SceneManager._scene._laserManager) {
            SceneManager._scene._laserManager.requestUpdate();
        }
    };

    console.log('EventLaserLines: Plugin loaded successfully');

})();
